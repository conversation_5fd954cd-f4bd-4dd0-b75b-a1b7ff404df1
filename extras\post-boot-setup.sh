#!/bin/bash
# post-boot-setup.sh: Vollständige r4w-Konfiguration nach dem ersten Boot
# Version: 0.2.0 - Post-Boot Setup für Windows-Build
# Autor: r4w-linux project
# Verwendung: sudo /home/<USER>/extras/post-boot-setup.sh

set -e

VERSION="0.2.0"
SETUP_MARKER="/home/<USER>/.r4w-setup-complete"

echo "🐍 r4w-linux Post-Boot Setup v$VERSION"
echo "======================================="

# Prüfe ob Setup bereits ausgeführt wurde
if [ -f "$SETUP_MARKER" ]; then
    echo "✅ r4w-Setup bereits abgeschlossen!"
    echo "💡 Zum erneuten Ausführen lösche: $SETUP_MARKER"
    exit 0
fi

# Prüfe Root-Rechte
if [ "$EUID" -ne 0 ]; then
    echo "❌ Root-Rechte erford<PERSON>lich!"
    echo "💡 Führe aus: sudo $0"
    exit 1
fi

echo "📦 Pakete aktualisieren und installieren..."
export DEBIAN_FRONTEND=noninteractive
apt-get update -qq

# Basis-Pakete installieren
apt-get install -y locales zsh neofetch htop tmux nmap netcat-traditional socat curl wget python3-pip \
    firefox-esr tor torsocks proxychains4 wireguard-tools chisel rclone sshuttle \
    metasploit-framework sqlmap nikto dirb gobuster hydra john hashcat \
    aircrack-ng reaver pixiewps wifite macchanger \
    git vim nano screen byobu tree file binutils strace ltrace \
    dnsutils whois traceroute tcpdump wireshark-common tshark \
    python3-requests python3-beautifulsoup4 python3-selenium python3-scapy

echo "🌍 Deutsche Lokalisierung..."
echo "de_DE.UTF-8 UTF-8" > /etc/locale.gen
locale-gen
update-locale LANG=de_DE.UTF-8
echo "LANG=de_DE.UTF-8" > /etc/default/locale

echo "🏠 Hostname und Zeitzone..."
echo "r4w" > /etc/hostname
sed -i 's/*********.*/*********\tr4w/' /etc/hosts
ln -fs /usr/share/zoneinfo/Europe/Berlin /etc/localtime

echo "🔐 SSH-Konfiguration..."
# SSH-Key Setup (falls noch nicht vorhanden)
if [ -f "/tmp/id_rsa_r4w.pub" ]; then
    mkdir -p /home/<USER>/.ssh
    cat /tmp/id_rsa_r4w.pub > /home/<USER>/.ssh/authorized_keys
    chown -R kali:kali /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    chmod 600 /home/<USER>/.ssh/authorized_keys
    rm -f /tmp/id_rsa_r4w.pub
fi

# SSH-Daemon konfigurieren
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
systemctl enable ssh

echo "🐚 ZSH als Standard-Shell..."
chsh -s /usr/bin/zsh kali

echo "🎨 Custom MOTD..."
if [ -f "/home/<USER>/extras/motd.r4w" ]; then
    cp /home/<USER>/extras/motd.r4w /etc/motd
fi

echo "⚡ Autostart-Script einrichten..."
if [ -f "/home/<USER>/extras/autostart.sh" ]; then
    chmod +x /home/<USER>/extras/autostart.sh
    echo "@reboot /home/<USER>/extras/autostart.sh" | crontab -u kali -
fi

echo "🌐 Statische IP konfigurieren..."
if [ -f "/boot/dhcpcd.conf.append" ]; then
    cat /boot/dhcpcd.conf.append >> /etc/dhcpcd.conf
fi

echo "🔧 Tor und Proxychains konfigurieren..."
# Tor als Service aktivieren
systemctl enable tor
# Proxychains für Tor konfigurieren
sed -i 's/#dynamic_chain/dynamic_chain/' /etc/proxychains4.conf
sed -i 's/strict_chain/#strict_chain/' /etc/proxychains4.conf
echo "socks5 127.0.0.1 9050" >> /etc/proxychains4.conf

echo "🦊 Firefox für headless Betrieb konfigurieren..."
# Firefox Profil für kali user erstellen
sudo -u kali firefox --headless --create-profile r4w 2>/dev/null || true

echo "🔐 Zusätzliche Security Tools konfigurieren..."
# Metasploit Datenbank initialisieren
msfdb init || true

echo "🔒 RiseVPN Installation..."
# RiseVPN Install-Script ausführbar machen und installieren
if [ -f "/home/<USER>/extras/install-risevpn.sh" ]; then
    chmod +x /home/<USER>/extras/install-risevpn.sh
    /home/<USER>/extras/install-risevpn.sh || echo "⚠️ RiseVPN Installation fehlgeschlagen"
fi

echo "🧅 Tor Browser CLI Tools konfigurieren..."
# Tor Browser CLI Alias erstellen
echo 'alias tor-browser="firefox --private-window --proxy-server=socks5://127.0.0.1:9050"' >> /home/<USER>/.zshrc
echo 'alias tor-curl="torsocks curl"' >> /home/<USER>/.zshrc
echo 'alias tor-wget="torsocks wget"' >> /home/<USER>/.zshrc

echo "🎯 ZSH-Konfiguration anwenden..."
if [ -f "/home/<USER>/extras/zsh-config.sh" ]; then
    chmod +x /home/<USER>/extras/zsh-config.sh
    sudo -u kali /home/<USER>/extras/zsh-config.sh
fi

echo "🧹 Aufräumen..."
apt-get autoremove -y
apt-get autoclean

# Setup-Marker erstellen
touch "$SETUP_MARKER"
chown kali:kali "$SETUP_MARKER"

echo ""
echo "✅ r4w-linux Post-Boot Setup abgeschlossen!"
echo "🔄 Neustart empfohlen für vollständige Aktivierung aller Änderungen"
echo ""
echo "📋 Nächste Schritte:"
echo "   1. sudo reboot"
echo "   2. SSH-Verbindung testen: ssh kali@192.168.1.42"
echo "   3. WLAN-Konfiguration anpassen falls nötig"
echo ""
echo "🎉 r4w ist bereit für den Einsatz!"
