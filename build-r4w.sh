#!/bin/bash
# build-r4w.sh: <PERSON>rst<PERSON>t Raspberry Pi Zero W Image mit r4w Setup
# Version: 0.1.0 - Fixed device-mapper issues
# Autor: r4w-linux project

set -e  # Exit on any error

# Konfiguration
IMG_BASE="kali-linux-*-raspberry-pi-zero-w.img.xz"
WORKDIR="r4w_build"
MOUNT_BOOT="$WORKDIR/boot"
MOUNT_ROOT="$WORKDIR/root"
OUTPUT_IMG="r4w-linux.img"
VERSION=$(cat VERSION 2>/dev/null || echo "0.1.0")

echo "🐍 r4w-linux Builder v$VERSION"
echo "================================"

# Prüfe Abhängigkeiten
command -v losetup >/dev/null 2>&1 || { echo "❌ losetup nicht gefunden. Installiere: sudo apt install util-linux"; exit 1; }
command -v xzcat >/dev/null 2>&1 || { echo "❌ xzcat nicht gefunden. Installiere: sudo apt install xz-utils"; exit 1; }
command -v fdisk >/dev/null 2>&1 || { echo "❌ fdisk nicht gefunden. Installiere: sudo apt install util-linux"; exit 1; }

# Prüfe ob Kali Image vorhanden
if ! ls $IMG_BASE 1> /dev/null 2>&1; then
    echo "❌ Kein Kali Linux Image gefunden!"
    echo "💡 Lade es von: https://www.kali.org/get-kali/#kali-arm"
    echo "   Dateiname sollte sein: kali-linux-*-raspberry-pi-zero-w.img.xz"
    exit 1
fi

# Cleanup function for proper error handling
cleanup() {
    echo "🧹 Cleanup wird ausgeführt..."
    sudo umount "$MOUNT_BOOT" 2>/dev/null || true
    sudo umount "$MOUNT_ROOT" 2>/dev/null || true
    if [ -n "$LOOP_DEV" ]; then
        sudo losetup -d "$LOOP_DEV" 2>/dev/null || true
    fi
}

# Set trap for cleanup on exit
trap cleanup EXIT

# Workspace vorbereiten
echo "🧹 Workspace aufräumen..."
cleanup
rm -rf "$WORKDIR"
mkdir -p "$MOUNT_BOOT" "$MOUNT_ROOT"

# Image entpacken
echo "📦 Kali Image entpacken..."
xzcat $IMG_BASE > "$OUTPUT_IMG"

# Partitionen mounten mit losetup (robuster als kpartx)
echo "💾 Partitionen mounten..."
LOOP_DEV=$(sudo losetup --find --show --partscan "$OUTPUT_IMG")
echo "📍 Loop device: $LOOP_DEV"

# Warten bis Partitionen verfügbar sind
sleep 3

# Partitionen identifizieren
BOOT_DEV="${LOOP_DEV}p1"
ROOT_DEV="${LOOP_DEV}p2"

# Prüfen ob Partitionen existieren
if [ ! -e "$BOOT_DEV" ] || [ ! -e "$ROOT_DEV" ]; then
    echo "❌ Partitionen nicht gefunden!"
    echo "🔍 Verfügbare Partitionen:"
    ls -la ${LOOP_DEV}* || true
    exit 1
fi

echo "✅ Boot partition: $BOOT_DEV"
echo "✅ Root partition: $ROOT_DEV"

sudo mount "$BOOT_DEV" "$MOUNT_BOOT"
sudo mount "$ROOT_DEV" "$MOUNT_ROOT"

# Boot-Partition konfigurieren
echo "🔧 Boot-Konfiguration..."

# SSH aktivieren
sudo touch "$MOUNT_BOOT/ssh"

# WLAN-Konfiguration mit Platzhaltern
sudo tee "$MOUNT_BOOT/wpa_supplicant.conf" > /dev/null <<EOF
country=DE
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1

network={
    ssid="WLAN_NAME"
    psk="WLAN_PASSWORT"
    key_mgmt=WPA-PSK
}
EOF

# Statische IP-Konfiguration vorbereiten
sudo tee "$MOUNT_BOOT/dhcpcd.conf.append" > /dev/null <<EOF
# Statische IP für r4w
interface wlan0
static ip_address=************/24
static routers=***********
static domain_name_servers=******* *******
EOF

# Root-Filesystem konfigurieren
echo "🔧 System-Konfiguration..."

# Extras-Ordner kopieren
sudo cp -r extras "$MOUNT_ROOT/home/<USER>/"
sudo cp dummy-key/id_rsa_r4w.pub "$MOUNT_ROOT/tmp/"

# Chroot-Setup ausführen
sudo chroot "$MOUNT_ROOT" /bin/bash <<'R4W_SETUP'
set -e
export DEBIAN_FRONTEND=noninteractive
export LANG=de_DE.UTF-8

echo "📦 Pakete aktualisieren und installieren..."
apt-get update -qq
apt-get install -y locales zsh neofetch htop tmux nmap netcat-traditional socat curl wget python3-pip \
    firefox-esr tor torsocks proxychains4 wireguard-tools chisel rclone sshuttle \
    metasploit-framework sqlmap nikto dirb gobuster hydra john hashcat \
    aircrack-ng reaver pixiewps wifite macchanger \
    git vim nano screen byobu tree file binutils strace ltrace \
    dnsutils whois traceroute tcpdump wireshark-common tshark \
    python3-requests python3-beautifulsoup4 python3-selenium python3-scapy

echo "🌍 Deutsche Lokalisierung..."
echo "de_DE.UTF-8 UTF-8" > /etc/locale.gen
locale-gen
update-locale LANG=de_DE.UTF-8
echo "LANG=de_DE.UTF-8" > /etc/default/locale

echo "🏠 Hostname und Zeitzone..."
echo "r4w" > /etc/hostname
sed -i 's/*********.*/*********\tr4w/' /etc/hosts
ln -fs /usr/share/zoneinfo/Europe/Berlin /etc/localtime

echo "🔐 SSH-Konfiguration..."
# SSH-Key Setup
mkdir -p /home/<USER>/.ssh
cat /tmp/id_rsa_r4w.pub > /home/<USER>/.ssh/authorized_keys
chown -R kali:kali /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys

# SSH-Daemon konfigurieren
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
systemctl enable ssh

echo "🐚 ZSH als Standard-Shell..."
chsh -s /usr/bin/zsh kali

echo "🎨 Custom MOTD..."
cp /home/<USER>/extras/motd.r4w /etc/motd

echo "⚡ Autostart-Script einrichten..."
chmod +x /home/<USER>/extras/autostart.sh
echo "@reboot /home/<USER>/extras/autostart.sh" | crontab -u kali -

echo "🌐 Statische IP konfigurieren..."
cat /boot/dhcpcd.conf.append >> /etc/dhcpcd.conf

echo "🔧 Tor und Proxychains konfigurieren..."
# Tor als Service aktivieren
systemctl enable tor
# Proxychains für Tor konfigurieren
sed -i 's/#dynamic_chain/dynamic_chain/' /etc/proxychains4.conf
sed -i 's/strict_chain/#strict_chain/' /etc/proxychains4.conf
echo "socks5 127.0.0.1 9050" >> /etc/proxychains4.conf

echo "🦊 Firefox für headless Betrieb konfigurieren..."
# Firefox Profil für kali user erstellen
sudo -u kali firefox --headless --create-profile r4w 2>/dev/null || true

echo "🔐 Zusätzliche Security Tools konfigurieren..."
# Metasploit Datenbank initialisieren
msfdb init || true

echo "🔒 RiseVPN Installation..."
# RiseVPN Install-Script ausführbar machen und installieren
chmod +x /home/<USER>/extras/install-risevpn.sh
/home/<USER>/extras/install-risevpn.sh || echo "⚠️ RiseVPN Installation fehlgeschlagen"

echo "🧅 Tor Browser CLI Tools konfigurieren..."
# Tor Browser CLI Alias erstellen
echo 'alias tor-browser="firefox --private-window --proxy-server=socks5://127.0.0.1:9050"' >> /home/<USER>/.zshrc
echo 'alias tor-curl="torsocks curl"' >> /home/<USER>/.zshrc
echo 'alias tor-wget="torsocks wget"' >> /home/<USER>/.zshrc

echo "🧹 Aufräumen..."
apt-get autoremove -y
apt-get autoclean
rm -f /tmp/id_rsa_r4w.pub

echo "✅ r4w-Setup abgeschlossen!"
R4W_SETUP

# Aufräumen und finalisieren
echo "🧹 Aufräumen..."
sudo umount "$MOUNT_BOOT"
sudo umount "$MOUNT_ROOT"
sudo losetup -d "$LOOP_DEV"

# Image komprimieren
echo "📦 Image komprimieren..."
xz -z -e -9 "$OUTPUT_IMG"

# Checksumme erstellen
echo "🔐 Checksumme erstellen..."
sha256sum "${OUTPUT_IMG}.xz" > "${OUTPUT_IMG}.xz.sha256"

echo ""
echo "🎉 r4w-linux v$VERSION erfolgreich erstellt!"
echo "📁 Dateien:"
echo "   - ${OUTPUT_IMG}.xz"
echo "   - ${OUTPUT_IMG}.xz.sha256"
echo ""
echo "📋 Nächste Schritte:"
echo "   1. Image mit Balena Etcher flashen"
echo "   2. WLAN in wpa_supplicant.conf konfigurieren"
echo "   3. SSH-Key ersetzen"
echo "   4. ssh hack"
echo ""
